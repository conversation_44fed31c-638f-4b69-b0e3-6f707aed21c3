#!/usr/bin/env node

import express from 'express';
import cors from 'cors';
import { SseServerTransport } from '@modelcontextprotocol/sdk/server/sse.js';
import { server } from './mcp.js';

const app = express();
const port = process.env.PORT || 3000;

// 启用CORS
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'Cache-Control']
}));

app.use(express.json());

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({ status: 'ok', service: 'Bazi MCP Server' });
});

// SSE端点
app.get('/sse', async (req, res) => {
  try {
    const transport = new SseServerTransport('/message', res);
    await server.connect(transport);
    console.log('Bazi MCP server connected via SSE');
  } catch (error) {
    console.error('Failed to connect Bazi MCP server via SSE:', error);
    res.status(500).json({ error: 'Failed to establish SSE connection' });
  }
});

// 消息处理端点
app.post('/message', async (req, res) => {
  try {
    // 这里处理来自客户端的消息
    res.json({ received: true });
  } catch (error) {
    console.error('Error processing message:', error);
    res.status(500).json({ error: 'Failed to process message' });
  }
});

app.listen(port, () => {
  console.log(`Bazi MCP SSE server is running on http://localhost:${port}`);
  console.log(`SSE endpoint: http://localhost:${port}/sse`);
  console.log(`Health check: http://localhost:${port}/health`);
});
