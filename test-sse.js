#!/usr/bin/env node

import express from 'express';
import cors from 'cors';

const app = express();
const port = process.env.PORT || 3000;

// 启用CORS
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'Cache-Control']
}));

app.use(express.json());

// 健康检查端点
app.get('/health', (_req, res) => {
  res.json({ status: 'ok', service: 'Bazi MCP Server' });
});

// SSE端点
app.get('/sse', async (_req, res) => {
  res.writeHead(200, {
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Cache-Control'
  });
  
  res.write('data: {"type": "connected"}\n\n');
  console.log('SSE client connected');
});

app.listen(port, () => {
  console.log(`Bazi MCP SSE server is running on http://localhost:${port}`);
  console.log(`SSE endpoint: http://localhost:${port}/sse`);
  console.log(`Health check: http://localhost:${port}/health`);
});
