{"name": "bazi-mcp", "version": "0.0.25", "type": "module", "devDependencies": {"@tsconfig/node22": "^22.0.1", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/node": "^22.14.1", "rimraf": "^6.0.1", "typescript": "^5.8.3"}, "repository": {"type": "git", "url": "git+https://github.com/cantian-ai/bazi-mcp.git"}, "keywords": ["bazi", "八字", "mcp", "算命", "命理"], "license": "ISC", "exports": {".": "./dist/index.js"}, "scripts": {"tsc": "tsc", "clean": "<PERSON><PERSON><PERSON> dist", "build": "npm run clean && npm run tsc", "prepublishOnly": "npm run build"}, "bin": {"bazi-mcp": "./dist/stdio.js", "bazi-mcp-sse": "./dist/sse.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.10.2", "cors": "^2.8.5", "date-fns": "^3.6.0", "date-fns-tz": "^3.2.0", "express": "^5.1.0", "tyme4ts": "^1.3.3", "zod": "^3.24.3"}}