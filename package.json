{"name": "bazi-mcp", "version": "0.0.25", "type": "module", "devDependencies": {"@tsconfig/node22": "^22.0.1", "@types/node": "^22.14.1", "typescript": "^5.8.3"}, "repository": {"type": "git", "url": "git+https://github.com/cantian-ai/bazi-mcp.git"}, "keywords": ["bazi", "八字", "mcp", "算命", "命理"], "license": "ISC", "exports": {".": "./dist/index.js"}, "scripts": {"tsc": "rm -rf dist && tsc", "prepublishOnly": "npm run tsc && chmod +x dist/stdio.js"}, "bin": {"bazi-mcp": "./dist/stdio.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.10.2", "date-fns": "^3.6.0", "date-fns-tz": "^3.2.0", "tyme4ts": "^1.3.3", "zod": "^3.24.3"}}